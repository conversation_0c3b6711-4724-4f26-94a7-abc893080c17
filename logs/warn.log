2025-09-04 11:27:09.485 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-04 11:27:09.491 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-04 11:27:09.495 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-04 11:27:12.035 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.035 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.036 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:12.037 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 11:27:18.716 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-04 11:27:18.717 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.728 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-04 11:27:18.728 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.739 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-04 11:27:18.740 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.752 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-04 11:27:18.752 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.764 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-04 11:27:18.764 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.775 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-04 11:27:18.775 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.786 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-04 11:27:18.786 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.797 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-04 11:27:18.797 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.807 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-04 11:27:18.807 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.819 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-04 11:27:18.819 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.835 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-04 11:27:18.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.851 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-04 11:27:18.851 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.863 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-04 11:27:18.883 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-04 11:27:18.883 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.897 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-04 11:27:18.897 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.909 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-04 11:27:18.909 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.921 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-04 11:27:18.922 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.937 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-04 11:27:18.938 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.953 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-04 11:27:18.954 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.966 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-04 11:27:18.967 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.978 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-04 11:27:18.979 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:18.989 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-04 11:27:18.990 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.000 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-04 11:27:19.000 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.010 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-04 11:27:19.010 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.027 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-04 11:27:19.028 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.046 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-04 11:27:19.046 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.057 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-04 11:27:19.057 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.066 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-04 11:27:19.066 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.090 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-04 11:27:19.090 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.100 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-04 11:27:19.101 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.111 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-04 11:27:19.111 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.122 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-04 11:27:19.122 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.137 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-04 11:27:19.137 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.147 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-04 11:27:19.148 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.163 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-04 11:27:19.163 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.177 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-04 11:27:19.178 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.192 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-04 11:27:19.193 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.206 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-04 11:27:19.206 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.221 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-04 11:27:19.223 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.236 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-04 11:27:19.237 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.247 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-04 11:27:19.248 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.260 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-04 11:27:19.261 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.273 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-04 11:27:19.273 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.285 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-04 11:27:19.286 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.297 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-04 11:27:19.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.307 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-04 11:27:19.308 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.319 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-04 11:27:19.319 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.330 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-04 11:27:19.331 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.346 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-04 11:27:19.346 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.374 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-04 11:27:19.374 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-04 11:27:19.400 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.413 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-04 11:27:19.413 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:19.428 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-04 11:27:19.429 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 11:27:31.451 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:31.453 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:34.543 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:34.543 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:37.630 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:37.631 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:40.709 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:40.710 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:43.811 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:43.813 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:46.905 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:46.906 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:50.008 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:50.009 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:53.109 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:53.109 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:56.458 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:56.459 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:27:59.620 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 11:27:59.620 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 11:28:28.981 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor68.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-04 11:28:55.202 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 26170ms to respond
[m2025-09-04 11:29:20.216 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource1DataSource) took 25013ms to respond
[m2025-09-04 11:29:45.142 [33mWARN [m [35m[RMI TCP Connection(11)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource2DataSource) took 24926ms to respond
[m2025-09-04 13:27:49.209 [33mWARN [m [35m[Thread-13][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-04 13:27:49.209 [33mWARN [m [35m[Thread-8][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-04 13:27:49.209 [33mWARN [m [35m[Thread-13][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-04 13:27:49.210 [33mWARN [m [35m[Thread-8][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-04 17:54:17.748 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-04 17:54:17.755 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-04 17:54:17.759 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-04 17:54:21.404 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.404 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.404 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.405 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.406 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:21.406 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:54:29.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-04 17:54:29.320 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.331 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-04 17:54:29.332 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.343 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-04 17:54:29.343 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.354 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-04 17:54:29.354 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.365 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-04 17:54:29.365 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.377 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-04 17:54:29.377 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.388 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-04 17:54:29.389 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-04 17:54:29.399 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.411 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-04 17:54:29.411 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.423 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-04 17:54:29.423 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.435 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-04 17:54:29.435 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.447 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-04 17:54:29.447 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.459 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-04 17:54:29.476 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-04 17:54:29.477 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.491 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-04 17:54:29.491 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.502 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-04 17:54:29.502 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.516 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-04 17:54:29.516 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.531 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-04 17:54:29.531 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.545 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-04 17:54:29.545 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.573 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-04 17:54:29.574 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.587 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-04 17:54:29.588 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.600 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-04 17:54:29.601 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.611 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-04 17:54:29.611 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.623 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-04 17:54:29.623 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.641 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-04 17:54:29.642 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.660 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-04 17:54:29.660 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.671 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-04 17:54:29.671 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.681 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-04 17:54:29.682 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.694 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-04 17:54:29.694 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.704 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-04 17:54:29.704 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.715 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-04 17:54:29.716 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.726 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-04 17:54:29.727 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.737 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-04 17:54:29.737 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.749 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-04 17:54:29.749 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.761 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-04 17:54:29.761 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.771 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-04 17:54:29.771 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.784 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-04 17:54:29.784 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.794 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-04 17:54:29.794 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.809 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-04 17:54:29.810 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.825 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-04 17:54:29.825 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-04 17:54:29.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.848 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-04 17:54:29.848 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.860 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-04 17:54:29.861 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.873 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-04 17:54:29.873 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.885 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-04 17:54:29.886 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.896 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-04 17:54:29.896 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.906 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-04 17:54:29.907 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.920 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-04 17:54:29.920 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.934 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-04 17:54:29.935 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.954 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-04 17:54:29.954 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.971 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-04 17:54:29.972 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.984 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-04 17:54:29.985 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:29.997 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-04 17:54:29.998 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:54:57.533 [33mWARN [m [35m[main][m [36mo.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext[m [34m[][m - [33mException encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'auxiliaryPoliceInfoController' defined in file [D:\work\code\hl-wj-police-archive\target\classes\com\hl\archive\controller\AuxiliaryPoliceInfoController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'auxiliaryPoliceInfoService': Invocation of init method failed; nested exception is java.lang.NullPointerException
[m2025-09-04 17:54:57.542 [33mWARN [m [35m[main][m [36mo.s.c.a.AnnotationConfigApplicationContext[m [34m[][m - [33mException thrown from ApplicationListener handling ContextClosedEvent
[m org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'rabbitConnectionFactory': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.retrieveApplicationListeners(AbstractApplicationEventMulticaster.java:264) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.AbstractApplicationEventMulticaster.getApplicationListeners(AbstractApplicationEventMulticaster.java:221) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:140) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:435) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1069) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:99) ~[spring-cloud-context-3.1.5.jar:3.1.5]
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:604) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) ~[spring-boot-2.7.18.jar:2.7.18]
	at com.hl.AppMain.main(AppMain.java:32) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
2025-09-04 17:54:57.803 [31mERROR[m [35m[main][m [36mo.s.b.SpringApplication[m [34m[][m - [31mApplication run failed
[m org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'auxiliaryPoliceInfoController' defined in file [D:\work\code\hl-wj-police-archive\target\classes\com\hl\archive\controller\AuxiliaryPoliceInfoController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'auxiliaryPoliceInfoService': Invocation of init method failed; nested exception is java.lang.NullPointerException
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) ~[spring-boot-2.7.18.jar:2.7.18]
	at com.hl.AppMain.main(AppMain.java:32) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'auxiliaryPoliceInfoService': Invocation of init method failed; nested exception is java.lang.NullPointerException
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:160) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 18 more
Caused by: java.lang.NullPointerException
	at com.hl.archive.utils.SsoCacheUtil.getCacheMap(SsoCacheUtil.java:20) ~[classes/:?]
	at com.hl.archive.utils.SsoCacheUtil.getOrganizationIdByNameWithLike(SsoCacheUtil.java:75) ~[classes/:?]
	at com.hl.archive.service.AuxiliaryPoliceInfoService.cleanAuxiliaryPoliceInfo(AuxiliaryPoliceInfoService.java:135) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:157) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:440) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 18 more
2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-11][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-17][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-17][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-04 17:54:57.810 [33mWARN [m [35m[Thread-11][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-04 17:58:16.611 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-04 17:58:16.617 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-04 17:58:16.621 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-04 17:58:19.807 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.808 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.808 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.809 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.809 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.809 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:19.810 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-04 17:58:27.285 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-04 17:58:27.285 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.294 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-04 17:58:27.296 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.306 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-04 17:58:27.307 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-04 17:58:27.318 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.329 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-04 17:58:27.329 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.340 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-04 17:58:27.340 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.351 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-04 17:58:27.352 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-04 17:58:27.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.373 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-04 17:58:27.373 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.383 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-04 17:58:27.383 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.395 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-04 17:58:27.395 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.408 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-04 17:58:27.408 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.419 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-04 17:58:27.436 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-04 17:58:27.437 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-04 17:58:27.449 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.460 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-04 17:58:27.461 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.473 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-04 17:58:27.473 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.484 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-04 17:58:27.485 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.496 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-04 17:58:27.496 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.510 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-04 17:58:27.510 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.525 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-04 17:58:27.526 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.539 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-04 17:58:27.540 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.549 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-04 17:58:27.549 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.559 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-04 17:58:27.559 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.579 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-04 17:58:27.579 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.595 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-04 17:58:27.595 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.607 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-04 17:58:27.607 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.616 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-04 17:58:27.617 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.628 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-04 17:58:27.629 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.638 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-04 17:58:27.639 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.649 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-04 17:58:27.649 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.659 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-04 17:58:27.660 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.671 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-04 17:58:27.671 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.680 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-04 17:58:27.681 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.691 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-04 17:58:27.691 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.701 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-04 17:58:27.702 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.712 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-04 17:58:27.712 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.724 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-04 17:58:27.724 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.740 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-04 17:58:27.741 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.755 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-04 17:58:27.755 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.767 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-04 17:58:27.768 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.780 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-04 17:58:27.780 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.792 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-04 17:58:27.793 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.804 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-04 17:58:27.804 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.815 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-04 17:58:27.816 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.826 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-04 17:58:27.827 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.837 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-04 17:58:27.837 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.848 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-04 17:58:27.849 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.865 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-04 17:58:27.865 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.882 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-04 17:58:27.882 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.898 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-04 17:58:27.899 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.918 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-04 17:58:27.919 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:27.930 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-04 17:58:27.930 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-04 17:58:42.578 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:42.578 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:45.684 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:45.685 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:48.779 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:48.780 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:51.882 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:51.882 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:54.977 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:54.978 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:58:58.082 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:58:58.082 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:01.169 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:01.169 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:04.254 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:04.254 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:07.633 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:07.633 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:10.784 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-04 17:59:10.784 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-04 17:59:45.163 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor69.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-04 18:00:12.040 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 26828ms to respond
[m2025-09-04 18:00:36.927 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource1DataSource) took 24887ms to respond
[m2025-09-04 18:01:01.800 [33mWARN [m [35m[RMI TCP Connection(33)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource2DataSource) took 24873ms to respond
[m2025-09-04 19:04:12.724 [33mWARN [m [35m[Thread-12][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-04 19:04:12.725 [33mWARN [m [35m[Thread-18][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-04 19:04:12.728 [33mWARN [m [35m[Thread-18][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-04 19:04:12.732 [33mWARN [m [35m[Thread-12][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-05 08:53:44.610 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-05 08:53:44.618 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-05 08:53:44.622 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-05 08:53:48.213 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.213 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.214 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.215 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.215 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.215 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:48.216 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-05 08:53:55.937 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-05 08:53:55.937 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.949 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-05 08:53:55.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.960 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-05 08:53:55.960 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.971 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-05 08:53:55.971 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.982 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-05 08:53:55.983 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:55.993 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-05 08:53:55.993 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.004 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-05 08:53:56.004 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.015 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-05 08:53:56.015 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.025 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-05 08:53:56.025 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.036 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-05 08:53:56.037 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.049 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-05 08:53:56.050 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.060 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-05 08:53:56.061 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.072 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-05 08:53:56.090 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-05 08:53:56.091 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.104 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-05 08:53:56.105 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.115 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-05 08:53:56.116 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.127 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-05 08:53:56.127 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.139 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-05 08:53:56.139 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.152 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-05 08:53:56.153 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.166 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-05 08:53:56.166 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.178 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-05 08:53:56.178 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.190 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-05 08:53:56.190 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.200 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-05 08:53:56.200 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.210 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-05 08:53:56.210 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.228 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-05 08:53:56.228 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.244 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-05 08:53:56.244 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.255 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-05 08:53:56.256 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.266 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-05 08:53:56.266 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.277 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-05 08:53:56.277 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.288 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-05 08:53:56.289 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.299 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-05 08:53:56.299 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.309 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-05 08:53:56.309 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-05 08:53:56.319 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.329 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-05 08:53:56.329 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.339 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-05 08:53:56.340 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.351 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-05 08:53:56.351 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-05 08:53:56.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.372 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-05 08:53:56.372 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.386 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-05 08:53:56.387 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.400 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-05 08:53:56.400 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.411 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-05 08:53:56.412 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.423 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-05 08:53:56.425 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.437 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-05 08:53:56.437 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-05 08:53:56.449 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.460 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-05 08:53:56.460 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.469 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-05 08:53:56.470 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.480 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-05 08:53:56.480 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.491 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-05 08:53:56.492 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.506 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-05 08:53:56.507 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.523 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-05 08:53:56.523 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.540 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-05 08:53:56.540 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.553 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-05 08:53:56.553 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:53:56.564 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-05 08:53:56.564 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-05 08:54:11.341 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:11.341 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:14.424 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:14.424 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:17.508 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:17.510 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:20.603 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:20.604 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:23.710 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:23.710 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:26.800 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:26.800 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:29.907 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:29.907 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:33.002 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:33.003 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:36.386 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:36.387 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:54:39.542 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-05 08:54:39.544 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-05 08:55:18.626 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor66.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-05 08:55:45.079 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 26418ms to respond
[m2025-09-05 08:56:10.035 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource1DataSource) took 24956ms to respond
[m2025-09-05 08:56:34.923 [33mWARN [m [35m[RMI TCP Connection(29)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/datasource2DataSource) took 24887ms to respond
[m2025-09-05 09:02:34.930 [31mERROR[m [35m[http-nio-28183-exec-1][m [36mc.h.c.c.e.GlobalExceptionHandler[m [34m[][m - [31m/auxiliaryPoliceInfo/statisticsPoliceByOrgId --> 
[m org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing: public com.hl.common.domain.R<com.hl.archive.domain.dto.PoliceStatisticsDTO> com.hl.archive.controller.AuxiliaryPoliceInfoController.statisticsPoliceByOrgId(com.hl.archive.domain.request.StatisticsQueryRequest)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:163) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:133) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
2025-09-05 09:05:12.157 [33mWARN [m [35m[Thread-20][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-05 09:05:12.158 [33mWARN [m [35m[Thread-20][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-05 09:05:12.157 [33mWARN [m [35m[Thread-14][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-05 09:05:12.159 [33mWARN [m [35m[Thread-14][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-11 13:06:41.373 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-11 13:06:41.380 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-11 13:06:41.389 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-11 13:06:44.988 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.989 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.989 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.989 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.989 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.990 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.990 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.991 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.992 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:44.992 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-11 13:06:53.093 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-11 13:06:53.094 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.105 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-11 13:06:53.106 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.116 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-11 13:06:53.117 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.127 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-11 13:06:53.128 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.138 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-11 13:06:53.139 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.150 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-11 13:06:53.150 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.161 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-11 13:06:53.161 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.172 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-11 13:06:53.172 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.184 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-11 13:06:53.184 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.194 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-11 13:06:53.195 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.206 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-11 13:06:53.207 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.218 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-11 13:06:53.218 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.229 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-11 13:06:53.247 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-11 13:06:53.248 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.262 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-11 13:06:53.263 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.273 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-11 13:06:53.274 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.285 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-11 13:06:53.285 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.297 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-11 13:06:53.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.312 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-11 13:06:53.313 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.329 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-11 13:06:53.330 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.345 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-11 13:06:53.346 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.365 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-11 13:06:53.366 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.382 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-11 13:06:53.383 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.398 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-11 13:06:53.399 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.414 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-11 13:06:53.414 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.435 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-11 13:06:53.436 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.457 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-11 13:06:53.457 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.471 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-11 13:06:53.471 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.483 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-11 13:06:53.484 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.498 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-11 13:06:53.498 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.513 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-11 13:06:53.513 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.546 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-11 13:06:53.546 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.560 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-11 13:06:53.560 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.572 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-11 13:06:53.573 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.584 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-11 13:06:53.585 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.598 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-11 13:06:53.598 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.610 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-11 13:06:53.611 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.622 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-11 13:06:53.623 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.633 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-11 13:06:53.633 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.647 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-11 13:06:53.648 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.662 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-11 13:06:53.663 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.673 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-11 13:06:53.675 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.686 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-11 13:06:53.686 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.699 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-11 13:06:53.699 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.710 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-11 13:06:53.710 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.722 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-11 13:06:53.722 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.734 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-11 13:06:53.734 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.747 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-11 13:06:53.748 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.763 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-11 13:06:53.763 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.780 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-11 13:06:53.781 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.799 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-11 13:06:53.800 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.820 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-11 13:06:53.820 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.839 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-11 13:06:53.839 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.852 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-11 13:06:53.852 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:06:53.863 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-11 13:06:53.863 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-11 13:07:07.612 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:07.613 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:10.738 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:10.738 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:13.852 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:13.852 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:16.971 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:16.972 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:20.097 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:20.097 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:23.227 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:23.228 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:26.354 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:26.354 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:29.512 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:29.512 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:32.974 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:32.974 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:07:36.116 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-11 13:07:36.116 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-11 13:08:11.199 [33mWARN [m [35m[RMI TCP Connection(34)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor66.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-11 13:09:04.869 [33mWARN [m [35m[Thread-13][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-11 13:09:04.869 [33mWARN [m [35m[Thread-18][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-11 13:09:04.869 [33mWARN [m [35m[Thread-18][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-11 13:09:04.871 [33mWARN [m [35m[Thread-13][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-12 09:13:27.713 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-12 09:13:27.719 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-12 09:13:27.724 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-12 09:13:31.258 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.258 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.259 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:31.261 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 09:13:38.857 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-12 09:13:38.857 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.868 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-12 09:13:38.868 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.878 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-12 09:13:38.878 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.889 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-12 09:13:38.890 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.899 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-12 09:13:38.900 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.911 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-12 09:13:38.911 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.921 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-12 09:13:38.922 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.931 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-12 09:13:38.931 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.942 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-12 09:13:38.942 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.953 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-12 09:13:38.953 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.964 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-12 09:13:38.964 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.976 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-12 09:13:38.976 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:38.987 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-12 09:13:39.003 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-12 09:13:39.004 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.017 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-12 09:13:39.018 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.028 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-12 09:13:39.028 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.040 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-12 09:13:39.041 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.051 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-12 09:13:39.051 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.064 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-12 09:13:39.064 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.076 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-12 09:13:39.076 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.089 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-12 09:13:39.090 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.103 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-12 09:13:39.104 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.114 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-12 09:13:39.114 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.124 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-12 09:13:39.125 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.133 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-12 09:13:39.146 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.164 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-12 09:13:39.165 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.180 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-12 09:13:39.180 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.190 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-12 09:13:39.190 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.200 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-12 09:13:39.200 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.210 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-12 09:13:39.211 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.221 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-12 09:13:39.221 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.231 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-12 09:13:39.231 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.242 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-12 09:13:39.242 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.252 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-12 09:13:39.252 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.261 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-12 09:13:39.261 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.271 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-12 09:13:39.271 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.282 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-12 09:13:39.282 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.291 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-12 09:13:39.292 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.300 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-12 09:13:39.301 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.314 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-12 09:13:39.316 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.328 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-12 09:13:39.329 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.339 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-12 09:13:39.339 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.350 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-12 09:13:39.351 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-12 09:13:39.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.373 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-12 09:13:39.373 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.382 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-12 09:13:39.383 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.393 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-12 09:13:39.393 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.404 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-12 09:13:39.404 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.415 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-12 09:13:39.416 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.427 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-12 09:13:39.427 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.442 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-12 09:13:39.442 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.459 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-12 09:13:39.459 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.475 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-12 09:13:39.475 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.488 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-12 09:13:39.488 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:13:39.499 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-12 09:13:39.499 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 09:22:22.585 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-12 09:22:22.585 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-12 09:22:22.585 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-12 09:22:22.587 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-12 17:04:32.516 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-12 17:04:32.522 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-12 17:04:32.527 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-12 17:04:36.080 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.081 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.081 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.081 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.081 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.081 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.082 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.082 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.082 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:36.082 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:04:43.749 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-12 17:04:43.749 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.760 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-12 17:04:43.760 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.770 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-12 17:04:43.771 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.782 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-12 17:04:43.783 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.793 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-12 17:04:43.793 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.804 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-12 17:04:43.805 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.815 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-12 17:04:43.815 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.826 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-12 17:04:43.826 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.837 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-12 17:04:43.837 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.848 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-12 17:04:43.848 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.860 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-12 17:04:43.860 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.872 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-12 17:04:43.872 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.884 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-12 17:04:43.902 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-12 17:04:43.903 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.931 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-12 17:04:43.932 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.943 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-12 17:04:43.943 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.955 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-12 17:04:43.955 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.969 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-12 17:04:43.969 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.983 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-12 17:04:43.984 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:43.997 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-12 17:04:43.997 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.011 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-12 17:04:44.011 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.024 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-12 17:04:44.024 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.035 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-12 17:04:44.036 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.047 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-12 17:04:44.047 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.058 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-12 17:04:44.058 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.077 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-12 17:04:44.077 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.092 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-12 17:04:44.092 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.104 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-12 17:04:44.104 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.114 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-12 17:04:44.114 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.125 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-12 17:04:44.125 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.135 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-12 17:04:44.135 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.146 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-12 17:04:44.146 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.158 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-12 17:04:44.158 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.170 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-12 17:04:44.171 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.181 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-12 17:04:44.181 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.193 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-12 17:04:44.193 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.204 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-12 17:04:44.204 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.214 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-12 17:04:44.214 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.226 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-12 17:04:44.226 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.241 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-12 17:04:44.243 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.257 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-12 17:04:44.257 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.267 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-12 17:04:44.267 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.278 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-12 17:04:44.278 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.292 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-12 17:04:44.292 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.303 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-12 17:04:44.303 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.313 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-12 17:04:44.313 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.324 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-12 17:04:44.324 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.335 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-12 17:04:44.336 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.350 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-12 17:04:44.350 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-12 17:04:44.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.378 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-12 17:04:44.378 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.397 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-12 17:04:44.397 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.414 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-12 17:04:44.414 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.426 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-12 17:04:44.427 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:04:44.438 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-12 17:04:44.438 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:05:43.338 [31mERROR[m [35m[main][m [36mo.s.b.SpringApplication[m [34m[][m - [31mApplication run failed
[m org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'police_ability_tag.ability_tag_name'.
### The error may exist in com/hl/archive/mapper/PoliceAbilityTagMapper.java (best guess)
### The error may involve com.hl.archive.mapper.PoliceAbilityTagMapper.updateById-Inline
### The error occurred while setting parameters
### SQL: UPDATE police_ability_tag  SET id_card=?, ability_tag_name=?, ability_tag_code=?, obtain_time=?,  is_deleted=?,   created_at=?, updated_at=?, task_id=?  WHERE id=?
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'police_ability_tag.ability_tag_name'.
; Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'police_ability_tag.ability_tag_name'.; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'police_ability_tag.ability_tag_name'.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy197.update(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.sun.proxy.$Proxy210.updateById(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.extension.service.IService.updateById(IService.java:236) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.hl.archive.service.PoliceAbilityTagService.cleanTagInfo(PoliceAbilityTagService.java:174) ~[classes/:?]
	at com.hl.archive.service.PoliceAbilityTagService$$FastClassBySpringCGLIB$$78c8b25.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceAbilityTagService$$EnhancerBySpringCGLIB$$b507e6f0.cleanTagInfo(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:344) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:229) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:166) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:429) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:386) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:114) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[?:1.8.0_432-432]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:82) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.builder.SpringApplicationBuilder.run(SpringApplicationBuilder.java:164) ~[spring-boot-2.7.18.jar:2.7.18]
	at com.hl.AppMain.main(AppMain.java:32) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'police_ability_tag.ability_tag_name'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:96) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:988) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:382) ~[mysql-connector-j-9.1.0.jar:9.1.0]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446) ~[druid-1.2.18.jar:?]
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434) ~[druid-1.2.18.jar:?]
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444) ~[druid-1.2.18.jar:?]
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158) ~[druid-1.2.18.jar:?]
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483) ~[druid-1.2.18.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy421.execute(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy418.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy417.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49) ~[mybatis-3.5.15.jar:3.5.15]
	at com.hl.log.config.logsql.LogSql.intercept(LogSql.java:59) ~[hl-log-0.0.5.1-20250731.011115-50.jar:0.0.5.1-SNAPSHOT]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy417.update(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at com.sun.proxy.$Proxy417.update(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197) ~[mybatis-3.5.15.jar:3.5.15]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 35 more
2025-09-12 17:06:24.278 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-12 17:06:24.278 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-12 17:06:24.278 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-12 17:06:24.279 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-12 17:06:33.846 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-12 17:06:33.858 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-12 17:06:33.861 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-12 17:06:37.006 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.007 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.008 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.008 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.008 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.009 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.009 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.009 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.010 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:37.011 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:06:44.581 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-12 17:06:44.582 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.592 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-12 17:06:44.592 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.603 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-12 17:06:44.603 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.613 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-12 17:06:44.613 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.623 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-12 17:06:44.623 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.634 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-12 17:06:44.634 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.644 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-12 17:06:44.644 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.654 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-12 17:06:44.655 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.665 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-12 17:06:44.666 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.677 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-12 17:06:44.677 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.686 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-12 17:06:44.686 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.699 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-12 17:06:44.699 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.709 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-12 17:06:44.726 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-12 17:06:44.727 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.740 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-12 17:06:44.741 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.750 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-12 17:06:44.750 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.763 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-12 17:06:44.763 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.773 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-12 17:06:44.773 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.785 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-12 17:06:44.785 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.798 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-12 17:06:44.798 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.811 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-12 17:06:44.811 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-12 17:06:44.837 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.847 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-12 17:06:44.847 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.856 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-12 17:06:44.857 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.867 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-12 17:06:44.867 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.884 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-12 17:06:44.884 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.899 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-12 17:06:44.899 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.910 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-12 17:06:44.910 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.920 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-12 17:06:44.921 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.929 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-12 17:06:44.931 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.940 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-12 17:06:44.941 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.951 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-12 17:06:44.951 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.961 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-12 17:06:44.961 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.970 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-12 17:06:44.970 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.979 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-12 17:06:44.979 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:44.990 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-12 17:06:44.990 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.001 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-12 17:06:45.001 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.013 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-12 17:06:45.014 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.023 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-12 17:06:45.023 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.038 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-12 17:06:45.040 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.053 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-12 17:06:45.053 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.063 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-12 17:06:45.063 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.075 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-12 17:06:45.075 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.086 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-12 17:06:45.086 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.098 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-12 17:06:45.098 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.107 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-12 17:06:45.108 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.118 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-12 17:06:45.118 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.128 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-12 17:06:45.128 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.140 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-12 17:06:45.141 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.151 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-12 17:06:45.152 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.167 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-12 17:06:45.167 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.183 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-12 17:06:45.184 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.200 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-12 17:06:45.201 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.213 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-12 17:06:45.214 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:06:45.224 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-12 17:06:45.224 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:07:36.686 [31mERROR[m [35m[RMI TCP Connection(38)-100.88.176.35][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 1009 millis. SELECT 1[]
[m2025-09-12 17:26:56.859 [33mWARN [m [35m[Thread-15][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-12 17:26:56.859 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-12 17:26:56.859 [33mWARN [m [35m[Thread-15][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-12 17:26:56.861 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-12 17:27:11.236 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-12 17:27:11.243 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-12 17:27:11.246 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.577 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.578 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:14.578 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-12 17:27:22.002 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-12 17:27:22.002 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.012 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-12 17:27:22.012 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.023 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-12 17:27:22.024 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.034 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-12 17:27:22.035 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.044 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-12 17:27:22.044 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.055 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-12 17:27:22.055 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.066 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-12 17:27:22.066 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.077 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-12 17:27:22.077 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.087 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-12 17:27:22.088 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.098 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-12 17:27:22.098 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.109 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-12 17:27:22.109 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.121 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-12 17:27:22.121 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.132 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-12 17:27:22.150 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-12 17:27:22.151 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.164 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-12 17:27:22.164 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.174 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-12 17:27:22.175 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.186 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-12 17:27:22.186 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.197 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-12 17:27:22.197 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.209 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-12 17:27:22.209 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.223 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-12 17:27:22.223 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.237 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-12 17:27:22.237 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.248 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-12 17:27:22.249 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.261 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-12 17:27:22.261 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.272 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-12 17:27:22.272 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.282 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-12 17:27:22.282 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.301 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-12 17:27:22.301 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.317 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-12 17:27:22.317 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.327 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-12 17:27:22.327 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.337 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-12 17:27:22.337 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.347 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-12 17:27:22.347 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.357 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-12 17:27:22.358 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.367 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-12 17:27:22.368 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.378 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-12 17:27:22.378 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.387 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-12 17:27:22.387 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.398 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-12 17:27:22.398 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.408 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-12 17:27:22.408 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.419 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-12 17:27:22.420 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.430 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-12 17:27:22.431 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.441 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-12 17:27:22.441 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.456 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-12 17:27:22.457 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.470 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-12 17:27:22.471 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.483 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-12 17:27:22.483 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.496 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-12 17:27:22.496 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.507 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-12 17:27:22.508 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.519 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-12 17:27:22.519 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.532 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-12 17:27:22.532 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.544 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-12 17:27:22.545 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.556 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-12 17:27:22.556 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.571 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-12 17:27:22.571 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.585 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-12 17:27:22.585 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.613 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-12 17:27:22.614 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.631 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-12 17:27:22.632 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.648 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-12 17:27:22.649 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.660 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-12 17:27:22.661 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:27:22.672 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-12 17:27:22.672 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-12 17:29:23.232 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-12 17:29:23.232 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-12 17:29:23.233 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-12 17:29:23.235 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-13 16:21:51.418 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-13 16:21:51.427 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-13 16:21:51.432 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-13 16:21:56.893 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.893 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.893 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.894 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.894 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.894 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.894 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.895 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.895 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:21:56.895 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 16:22:09.267 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-13 16:22:09.268 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.287 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-13 16:22:09.288 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.306 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-13 16:22:09.306 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.326 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-13 16:22:09.326 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.342 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-13 16:22:09.343 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.361 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-13 16:22:09.361 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.379 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-13 16:22:09.380 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.396 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-13 16:22:09.396 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.413 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-13 16:22:09.413 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.430 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-13 16:22:09.432 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-13 16:22:09.450 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.468 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-13 16:22:09.468 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.489 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-13 16:22:09.526 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-13 16:22:09.526 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.550 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-13 16:22:09.551 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.567 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-13 16:22:09.568 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.588 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-13 16:22:09.588 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.607 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-13 16:22:09.607 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.628 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-13 16:22:09.629 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.649 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-13 16:22:09.649 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.672 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-13 16:22:09.672 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.691 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-13 16:22:09.691 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.709 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-13 16:22:09.709 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.728 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-13 16:22:09.728 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.746 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-13 16:22:09.747 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.786 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-13 16:22:09.786 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.813 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-13 16:22:09.814 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.830 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-13 16:22:09.830 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.847 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-13 16:22:09.847 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.865 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-13 16:22:09.865 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.881 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-13 16:22:09.881 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.915 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-13 16:22:09.915 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.932 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-13 16:22:09.932 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.948 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-13 16:22:09.948 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.964 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-13 16:22:09.964 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.981 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-13 16:22:09.982 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:09.999 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-13 16:22:09.999 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.016 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-13 16:22:10.018 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.033 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-13 16:22:10.033 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.064 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-13 16:22:10.064 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.087 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-13 16:22:10.088 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.105 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-13 16:22:10.105 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.125 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-13 16:22:10.125 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.148 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-13 16:22:10.149 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.168 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-13 16:22:10.168 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.185 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-13 16:22:10.185 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.200 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-13 16:22:10.201 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.218 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-13 16:22:10.219 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.240 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-13 16:22:10.240 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.262 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-13 16:22:10.262 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.293 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-13 16:22:10.293 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.326 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-13 16:22:10.327 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.359 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-13 16:22:10.359 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.379 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-13 16:22:10.380 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:10.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-13 16:22:10.401 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 16:22:33.813 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:33.813 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:36.966 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:36.966 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:40.137 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:40.137 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:43.316 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:43.316 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:46.486 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:46.487 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:49.651 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:49.652 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:52.827 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:52.827 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:55.992 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:55.992 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:22:59.791 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:22:59.791 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:23:03.147 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 16:23:03.148 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 16:23:52.742 [33mWARN [m [35m[RMI TCP Connection(7)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-13 16:24:00.315 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 2109 millis. SELECT 1[]
[m2025-09-13 16:24:03.952 [33mWARN [m [35m[RMI TCP Connection(7)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 11062ms to respond
[m2025-09-13 16:33:56.846 [31mERROR[m [35m[http-nio-28183-exec-1][m [36mc.h.s.c.s.SsoTokenService[m [34m[][m - [31m[401] during [POST] to [http://sso-hl/token] [SsoCacheApi#token(String,String)]: [{"errno":401,"error":"[4]解析认证信息失败，请重新登录"}]
[m2025-09-13 16:33:57.019 [33mWARN [m [35m[http-nio-28183-exec-1][m [36mc.h.s.u.AuthenException[m [34m[][m - [33m(192.168.10.69 -> /policeNewProfile/exportWord) -> {errno=401, error=[4]解析认证信息失败，请重新登录}
[m2025-09-13 16:35:00.230 [33mWARN [m [35m[http-nio-28183-exec-9][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-13 16:47:37.951 [33mWARN [m [35m[Thread-23][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-13 16:47:37.951 [33mWARN [m [35m[Thread-29][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-13 16:47:37.952 [33mWARN [m [35m[Thread-29][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-13 16:47:37.963 [33mWARN [m [35m[Thread-23][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-13 17:20:00.930 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-13 17:20:00.939 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-13 17:20:00.945 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-13 17:20:05.210 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.210 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.210 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.211 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.211 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.211 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.211 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.211 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.211 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:05.212 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:20:16.362 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-13 17:20:16.363 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.380 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-13 17:20:16.381 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.398 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-13 17:20:16.399 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.417 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-13 17:20:16.417 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.432 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-13 17:20:16.433 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-13 17:20:16.451 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.468 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-13 17:20:16.468 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.496 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-13 17:20:16.497 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.513 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-13 17:20:16.514 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.530 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-13 17:20:16.530 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.548 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-13 17:20:16.549 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.566 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-13 17:20:16.567 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.588 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-13 17:20:16.622 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-13 17:20:16.623 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.645 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-13 17:20:16.646 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.661 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-13 17:20:16.662 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.682 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-13 17:20:16.683 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.702 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-13 17:20:16.702 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.722 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-13 17:20:16.723 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.742 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-13 17:20:16.742 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.765 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-13 17:20:16.765 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.785 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-13 17:20:16.786 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.802 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-13 17:20:16.803 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.818 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-13 17:20:16.819 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.835 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-13 17:20:16.835 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.873 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-13 17:20:16.874 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.899 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-13 17:20:16.900 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.917 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-13 17:20:16.917 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.932 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-13 17:20:16.933 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.949 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-13 17:20:16.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.965 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-13 17:20:16.966 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.983 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-13 17:20:16.983 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:16.999 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-13 17:20:16.999 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.016 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-13 17:20:17.016 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.031 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-13 17:20:17.031 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.048 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-13 17:20:17.049 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.066 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-13 17:20:17.067 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.085 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-13 17:20:17.086 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.099 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-13 17:20:17.100 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.127 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-13 17:20:17.128 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.151 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-13 17:20:17.151 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.168 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-13 17:20:17.169 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.187 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-13 17:20:17.187 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.208 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-13 17:20:17.208 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.228 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-13 17:20:17.229 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.262 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-13 17:20:17.262 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.279 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-13 17:20:17.279 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.295 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-13 17:20:17.296 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.319 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-13 17:20:17.319 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.336 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-13 17:20:17.338 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.367 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-13 17:20:17.367 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.400 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-13 17:20:17.400 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.430 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-13 17:20:17.431 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.450 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-13 17:20:17.450 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:17.468 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-13 17:20:17.469 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:20:39.199 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:39.201 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:20:42.353 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:42.354 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:20:45.533 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:45.533 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:20:48.691 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:48.691 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:20:51.873 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:51.874 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:20:55.049 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:55.049 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:20:58.234 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:20:58.234 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:21:01.394 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:21:01.394 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:21:05.122 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:21:05.123 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:21:08.520 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:21:08.523 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:21:53.267 [33mWARN [m [35m[RMI TCP Connection(3)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor146.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-13 17:21:59.367 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 1951 millis. SELECT 1[]
[m2025-09-13 17:22:41.512 [31mERROR[m [35m[http-nio-28183-exec-1][m [36mc.h.c.c.e.GlobalExceptionHandler[m [34m[][m - [31m/policeNewProfile/exportListWord --> 
[m com.deepoove.poi.exception.ResolverException: Cannot find the file [conf\template\new_police_list.docx.docx]
	at com.deepoove.poi.XWPFTemplate.compile(XWPFTemplate.java:134) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.XWPFTemplate.compile(XWPFTemplate.java:120) ~[poi-tl-1.12.2.jar:?]
	at com.hl.archive.service.PoliceNewProfileService.exportPoliceListWord(PoliceNewProfileService.java:153) ~[classes/:?]
	at com.hl.archive.service.PoliceNewProfileService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceNewProfileService$$EnhancerBySpringCGLIB$$1.exportPoliceListWord(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceNewProfileController.export(PoliceNewProfileController.java:72) ~[classes/:?]
	at com.hl.archive.controller.PoliceNewProfileController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceNewProfileController$$EnhancerBySpringCGLIB$$1.export(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.io.FileNotFoundException: conf\template\new_police_list.docx.docx (系统找不到指定的文件。)
	at java.io.FileInputStream.open0(FileInputStream.java) ~[?:1.8.0_432-432]
	at java.io.FileInputStream.open(FileInputStream.java:195) ~[?:1.8.0_432-432]
	at java.io.FileInputStream.<init>(FileInputStream.java:138) ~[?:1.8.0_432-432]
	at com.deepoove.poi.XWPFTemplate.compile(XWPFTemplate.java:132) ~[poi-tl-1.12.2.jar:?]
	... 136 more
2025-09-13 17:23:38.146 [33mWARN [m [35m[http-nio-28183-exec-4][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-13 17:24:40.210 [33mWARN [m [35m[http-nio-28183-exec-15][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-13 17:29:02.380 [33mWARN [m [35m[Thread-21][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-13 17:29:02.442 [33mWARN [m [35m[Thread-21][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-13 17:29:02.417 [33mWARN [m [35m[Thread-28][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-13 17:29:02.442 [33mWARN [m [35m[Thread-28][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-13 17:29:34.140 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-13 17:29:34.149 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-13 17:29:34.154 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-13 17:29:38.113 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.114 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.114 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.114 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.114 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.114 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.115 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.115 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.115 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:38.115 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-13 17:29:49.008 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-13 17:29:49.009 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.026 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-13 17:29:49.026 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.044 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-13 17:29:49.045 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.061 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-13 17:29:49.061 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.078 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-13 17:29:49.079 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.096 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-13 17:29:49.096 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.114 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-13 17:29:49.115 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.131 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-13 17:29:49.131 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.148 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-13 17:29:49.148 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.165 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-13 17:29:49.165 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.184 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-13 17:29:49.184 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.203 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-13 17:29:49.203 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.222 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-13 17:29:49.256 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-13 17:29:49.256 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.280 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-13 17:29:49.281 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.296 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-13 17:29:49.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.318 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-13 17:29:49.318 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.336 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-13 17:29:49.337 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.357 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-13 17:29:49.358 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.377 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-13 17:29:49.377 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-13 17:29:49.399 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.417 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-13 17:29:49.418 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.435 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-13 17:29:49.436 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.452 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-13 17:29:49.452 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.468 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-13 17:29:49.469 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.507 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-13 17:29:49.507 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.534 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-13 17:29:49.534 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.551 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-13 17:29:49.552 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.567 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-13 17:29:49.568 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.584 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-13 17:29:49.584 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.600 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-13 17:29:49.600 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.618 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-13 17:29:49.619 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.634 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-13 17:29:49.635 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.650 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-13 17:29:49.651 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.667 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-13 17:29:49.668 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.685 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-13 17:29:49.687 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.703 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-13 17:29:49.704 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.720 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-13 17:29:49.721 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.749 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-13 17:29:49.749 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.777 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-13 17:29:49.777 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.800 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-13 17:29:49.801 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.816 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-13 17:29:49.817 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.835 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-13 17:29:49.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.856 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-13 17:29:49.857 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.876 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-13 17:29:49.877 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.893 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-13 17:29:49.895 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.910 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-13 17:29:49.910 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.927 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-13 17:29:49.928 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.949 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-13 17:29:49.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.968 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-13 17:29:49.968 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:49.998 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-13 17:29:49.999 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:50.030 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-13 17:29:50.030 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:50.063 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-13 17:29:50.063 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:50.084 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-13 17:29:50.085 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:29:50.106 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-13 17:29:50.106 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-13 17:30:10.961 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:10.961 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:14.119 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:14.120 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:17.302 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:17.303 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:20.480 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:20.481 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:23.657 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:23.658 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:26.842 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:26.843 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:30.049 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:30.049 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:33.197 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:33.197 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:36.900 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:36.902 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:30:40.229 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-13 17:30:40.231 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-13 17:31:26.272 [33mWARN [m [35m[RMI TCP Connection(7)-100.88.176.35][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor144.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/*************:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-13 17:31:33.211 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 1891 millis. SELECT 1[]
[m2025-09-13 17:31:36.856 [33mWARN [m [35m[RMI TCP Connection(7)-100.88.176.35][m [36mo.s.b.a.h.HealthEndpointSupport[m [34m[][m - [33mHealth contributor org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator (db/masterDataSource) took 10474ms to respond
[m2025-09-13 17:32:00.986 [33mWARN [m [35m[boundedElastic-1][m [36mo.s.b.a.r.RedisReactiveHealthIndicator[m [34m[][m - [33mRedis health check failed
[m org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to *************:10016
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:1117) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:509) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:103) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:86) ~[reactor-core-3.4.34.jar:3.4.34]
	at reactor.core.publisher.FluxSubscribeOnCallable$CallableSubscribeOnSubscription.run(FluxSubscribeOnCallable.java:227) ~[reactor-core-3.4.34.jar:3.4.34]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:68) ~[reactor-core-3.4.34.jar:3.4.34]
	at reactor.core.scheduler.SchedulerTask.call(SchedulerTask.java:28) ~[reactor-core-3.4.34.jar:3.4.34]
	at java.util.concurrent.FutureTask.run(FutureTask.java:266) ~[?:1.8.0_432-432]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180) ~[?:1.8.0_432-432]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to *************:10016
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78) ~[lettuce-core-6.1.10.RELEASE.jar:6.1.10.RELEASE]
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56) ~[lettuce-core-6.1.10.RELEASE.jar:6.1.10.RELEASE]
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330) ~[lettuce-core-6.1.10.RELEASE.jar:6.1.10.RELEASE]
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216) ~[lettuce-core-6.1.10.RELEASE.jar:6.1.10.RELEASE]
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at java.util.Optional.orElseGet(Optional.java:267) ~[?:1.8.0_432-432]
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115) ~[spring-data-redis-2.7.18.jar:2.7.18]
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595) ~[spring-data-redis-2.7.18.jar:2.7.18]
	... 15 more
Caused by: io.lettuce.core.RedisCommandTimeoutException: Connection initialization timed out. Command timed out after 5 second(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:71) ~[lettuce-core-6.1.10.RELEASE.jar:6.1.10.RELEASE]
	at io.lettuce.core.protocol.RedisHandshakeHandler.lambda$channelRegistered$0(RedisHandshakeHandler.java:62) ~[lettuce-core-6.1.10.RELEASE.jar:6.1.10.RELEASE]
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	at io.netty.util.concurrent.PromiseTask.run(PromiseTask.java:106) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.101.Final.jar:4.1.101.Final]
	... 1 more
2025-09-13 17:37:35.124 [33mWARN [m [35m[http-nio-28183-exec-4][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-13 17:37:36.834 [31mERROR[m [35m[http-nio-28183-exec-4][m [36mc.h.c.c.e.GlobalExceptionHandler[m [34m[][m - [31m/policeNewProfile/exportListWord --> 
[m com.deepoove.poi.exception.RenderException: HackLoopTable for {{polices}} error: EL1008E: Property or field 'name' cannot be found on object of type 'com.alibaba.fastjson2.JSONObject' - maybe not public or not valid?
	at com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy.render(LoopRowTableRenderPolicy.java:142) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.DelegatePolicy.invoke(DelegatePolicy.java:47) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.ElementProcessor.visit(ElementProcessor.java:65) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.ElementProcessor.visit(ElementProcessor.java:59) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.template.run.RunTemplate.accept(RunTemplate.java:64) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.DocumentProcessor.visit(DocumentProcessor.java:95) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.template.run.RunTemplate.accept(RunTemplate.java:64) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.DocumentProcessor.lambda$process$0(DocumentProcessor.java:59) ~[poi-tl-1.12.2.jar:?]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[?:1.8.0_432-432]
	at com.deepoove.poi.render.processor.DocumentProcessor.process(DocumentProcessor.java:59) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.DefaultRender.renderTemplate(DefaultRender.java:82) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.DefaultRender.render(DefaultRender.java:64) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.XWPFTemplate.render(XWPFTemplate.java:209) ~[poi-tl-1.12.2.jar:?]
	at com.hl.archive.service.PoliceNewProfileService.exportPoliceListWord(PoliceNewProfileService.java:173) ~[classes/:?]
	at com.hl.archive.service.PoliceNewProfileService$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.service.PoliceNewProfileService$$EnhancerBySpringCGLIB$$1.exportPoliceListWord(<generated>) ~[classes/:?]
	at com.hl.archive.controller.PoliceNewProfileController.export(PoliceNewProfileController.java:72) ~[classes/:?]
	at com.hl.archive.controller.PoliceNewProfileController$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.hl.archive.controller.PoliceNewProfileController$$EnhancerBySpringCGLIB$$1.export(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.83.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.hl.common.config.filter.CorsFilter.doFilter(CorsFilter.java:53) ~[hl-basic-0.0.5.1-20250731.011111-52.jar:0.0.5.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.18.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at com.hl.security.config.sso.SsoAuthTokenFilter.doFilterInternal(SsoAuthTokenFilter.java:52) ~[hl-sso-0.0.5.1-20250412.014148-20.jar:0.0.5.1-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186) ~[spring-security-web-5.7.11.jar:5.7.11]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.31.jar:5.3.31]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.31.jar:5.3.31]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) ~[tomcat-embed-core-9.0.83.jar:9.0.83]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: org.springframework.expression.spel.SpelEvaluationException: EL1008E: Property or field 'name' cannot be found on object of type 'com.alibaba.fastjson2.JSONObject' - maybe not public or not valid?
	at org.springframework.expression.spel.ast.PropertyOrFieldReference.readProperty(PropertyOrFieldReference.java:223) ~[spring-expression-5.3.31.jar:5.3.31]
	at org.springframework.expression.spel.ast.PropertyOrFieldReference.getValueInternal(PropertyOrFieldReference.java:106) ~[spring-expression-5.3.31.jar:5.3.31]
	at org.springframework.expression.spel.ast.PropertyOrFieldReference.getValueInternal(PropertyOrFieldReference.java:93) ~[spring-expression-5.3.31.jar:5.3.31]
	at org.springframework.expression.spel.ast.SpelNodeImpl.getValue(SpelNodeImpl.java:114) ~[spring-expression-5.3.31.jar:5.3.31]
	at org.springframework.expression.spel.standard.SpelExpression.getValue(SpelExpression.java:273) ~[spring-expression-5.3.31.jar:5.3.31]
	at com.deepoove.poi.render.compute.SpELRenderDataCompute.compute(SpELRenderDataCompute.java:73) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.ElementProcessor.visit(ElementProcessor.java:65) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.ElementProcessor.visit(ElementProcessor.java:59) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.template.run.RunTemplate.accept(RunTemplate.java:64) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.DocumentProcessor.visit(DocumentProcessor.java:95) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.template.run.RunTemplate.accept(RunTemplate.java:64) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.render.processor.DocumentProcessor.lambda$process$0(DocumentProcessor.java:59) ~[poi-tl-1.12.2.jar:?]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[?:1.8.0_432-432]
	at com.deepoove.poi.render.processor.DocumentProcessor.process(DocumentProcessor.java:59) ~[poi-tl-1.12.2.jar:?]
	at com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy.lambda$render$0(LoopRowTableRenderPolicy.java:134) ~[poi-tl-1.12.2.jar:?]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[?:1.8.0_432-432]
	at com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy.render(LoopRowTableRenderPolicy.java:132) ~[poi-tl-1.12.2.jar:?]
	... 147 more
2025-09-13 17:39:41.126 [33mWARN [m [35m[http-nio-28183-exec-19][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-13 17:40:05.729 [33mWARN [m [35m[Thread-21][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-13 17:40:05.730 [33mWARN [m [35m[Thread-28][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-13 17:40:05.735 [33mWARN [m [35m[Thread-28][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-13 17:40:05.751 [33mWARN [m [35m[Thread-21][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-15 13:58:51.435 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-15 13:58:51.443 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-15 13:58:51.447 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.802 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.804 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:58:54.804 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-15 13:59:02.070 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-15 13:59:02.070 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.080 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-15 13:59:02.081 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.091 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-15 13:59:02.092 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.103 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-15 13:59:02.104 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.113 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-15 13:59:02.114 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.125 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-15 13:59:02.125 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.136 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-15 13:59:02.136 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.146 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-15 13:59:02.147 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.158 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-15 13:59:02.158 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.168 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-15 13:59:02.169 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.180 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-15 13:59:02.180 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.191 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-15 13:59:02.191 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.203 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-15 13:59:02.221 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-15 13:59:02.221 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.236 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-15 13:59:02.236 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.246 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-15 13:59:02.246 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.258 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-15 13:59:02.259 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.269 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-15 13:59:02.270 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.283 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-15 13:59:02.284 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.296 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-15 13:59:02.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.308 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-15 13:59:02.309 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.322 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-15 13:59:02.322 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.332 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-15 13:59:02.333 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.342 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-15 13:59:02.343 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.353 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-15 13:59:02.353 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.371 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-15 13:59:02.371 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.387 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-15 13:59:02.387 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.399 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-15 13:59:02.399 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.410 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-15 13:59:02.410 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.421 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-15 13:59:02.421 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.431 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-15 13:59:02.432 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.442 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-15 13:59:02.442 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.452 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-15 13:59:02.452 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.462 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-15 13:59:02.462 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.473 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-15 13:59:02.473 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.484 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-15 13:59:02.484 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.494 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-15 13:59:02.494 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.504 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-15 13:59:02.504 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.514 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-15 13:59:02.514 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.528 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-15 13:59:02.529 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.553 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-15 13:59:02.553 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.564 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-15 13:59:02.565 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.576 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-15 13:59:02.576 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.588 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-15 13:59:02.589 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.601 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-15 13:59:02.601 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.611 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-15 13:59:02.611 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.622 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-15 13:59:02.622 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.634 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-15 13:59:02.634 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.646 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-15 13:59:02.646 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.657 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-15 13:59:02.658 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.672 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-15 13:59:02.672 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.689 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-15 13:59:02.689 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.706 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-15 13:59:02.706 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.718 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-15 13:59:02.719 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 13:59:02.730 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-15 13:59:02.730 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-15 15:21:08.062 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-15 15:21:08.063 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-15 15:21:08.063 [33mWARN [m [35m[Thread-16][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-15 15:21:08.065 [33mWARN [m [35m[Thread-10][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m