package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrGattxz;
import com.hl.orasync.domain.VWjBrGattxzToPoliceHkMacauTaiwanPermitMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__534;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__534.class,
    uses = {ConversionUtils.class,VWjBrGattxzToPoliceHkMacauTaiwanPermitMapper.class},
    imports = {}
)
public interface PoliceHkMacauTaiwanPermitToVWjBrGattxzMapper extends BaseMapper<PoliceHkMacauTaiwanPermit, VWjBrGattxz> {
  @Mapping(
      target = "bgjgmc",
      source = "custodyOrganization"
  )
  @Mapping(
      target = "qfrq",
      source = "issueDate"
  )
  @Mapping(
      target = "yxqz",
      source = "expiryDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bz",
      source = "remarks"
  )
  @Mapping(
      target = "zjhm",
      source = "documentNumber"
  )
  @Mapping(
      target = "zjmc",
      source = "documentName"
  )
  VWjBrGattxz convert(PoliceHkMacauTaiwanPermit source);

  @Mapping(
      target = "bgjgmc",
      source = "custodyOrganization"
  )
  @Mapping(
      target = "qfrq",
      source = "issueDate"
  )
  @Mapping(
      target = "yxqz",
      source = "expiryDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "bz",
      source = "remarks"
  )
  @Mapping(
      target = "zjhm",
      source = "documentNumber"
  )
  @Mapping(
      target = "zjmc",
      source = "documentName"
  )
  VWjBrGattxz convert(PoliceHkMacauTaiwanPermit source, @MappingTarget VWjBrGattxz target);
}
