package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRytc;
import com.hl.orasync.domain.VWjRytcToPoliceSpecialtiesMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__534;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__534.class,
    uses = {ConversionUtils.class,VWjRytcToPoliceSpecialtiesMapper.class},
    imports = {}
)
public interface PoliceSpecialtiesToVWjRytcMapper extends BaseMapper<PoliceSpecialties, VWjRytc> {
  @Mapping(
      target = "jcsj",
      source = "awardDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jcpjjgmc",
      source = "approveAuthority"
  )
  @Mapping(
      target = "jcmc",
      source = "specialtyName"
  )
  VWjRytc convert(PoliceSpecialties source);

  @Mapping(
      target = "jcsj",
      source = "awardDate"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "jcpjjgmc",
      source = "approveAuthority"
  )
  @Mapping(
      target = "jcmc",
      source = "specialtyName"
  )
  VWjRytc convert(PoliceSpecialties source, @MappingTarget VWjRytc target);
}
