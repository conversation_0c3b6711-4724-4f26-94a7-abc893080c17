package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgrXjsj;
import com.hl.orasync.domain.VWjXhjhRxgrXjsjToPoliceProjectStoryMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__534;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__534.class,
    uses = {ConversionUtils.class,VWjXhjhRxgrXjsjToPoliceProjectStoryMapper.class},
    imports = {}
)
public interface PoliceProjectStoryToVWjXhjhRxgrXjsjMapper extends BaseMapper<PoliceProjectStory, VWjXhjhRxgrXjsj> {
  @Mapping(
      target = "djsj",
      source = "registerTime"
  )
  @Mapping(
      target = "djrxm",
      source = "registeredBy"
  )
  @Mapping(
      target = "jlXxzjbh",
      source = "xhjhZjbh"
  )
  @Mapping(
      target = "xjsjBt",
      source = "title"
  )
  @Mapping(
      target = "xjsjNr",
      source = "content"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgrXjsj convert(PoliceProjectStory source);

  @Mapping(
      target = "djsj",
      source = "registerTime"
  )
  @Mapping(
      target = "djrxm",
      source = "registeredBy"
  )
  @Mapping(
      target = "jlXxzjbh",
      source = "xhjhZjbh"
  )
  @Mapping(
      target = "xjsjBt",
      source = "title"
  )
  @Mapping(
      target = "xjsjNr",
      source = "content"
  )
  @Mapping(
      target = "xxzjbh",
      source = "zjbh"
  )
  VWjXhjhRxgrXjsj convert(PoliceProjectStory source, @MappingTarget VWjXhjhRxgrXjsj target);
}
