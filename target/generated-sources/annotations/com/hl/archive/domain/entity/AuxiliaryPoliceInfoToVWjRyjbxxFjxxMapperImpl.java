package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjbxxFjxx;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T08:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class AuxiliaryPoliceInfoToVWjRyjbxxFjxxMapperImpl implements AuxiliaryPoliceInfoToVWjRyjbxxFjxxMapper {

    @Override
    public VWjRyjbxxFjxx convert(AuxiliaryPoliceInfo source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjbxxFjxx vWjRyjbxxFjxx = new VWjRyjbxxFjxx();

        if ( source.getBirthDate() != null ) {
            vWjRyjbxxFjxx.setCsrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getBirthDate() ) );
        }
        vWjRyjbxxFjxx.setZdbs( source.getMedicalHistory() );
        vWjRyjbxxFjxx.setLwgs( source.getLaborCompany() );
        vWjRyjbxxFjxx.setDwmc( source.getOrganization() );
        vWjRyjbxxFjxx.setZrldjh( source.getLeaderPoliceNumber() );
        vWjRyjbxxFjxx.setGmsfhm( source.getIdCard() );
        vWjRyjbxxFjxx.setZjWx( source.getOfficePhoneOuter() );
        if ( source.getAuxiliarySeniority() != null ) {
            vWjRyjbxxFjxx.setFzgl( new BigDecimal( source.getAuxiliarySeniority() ) );
        }
        vWjRyjbxxFjxx.setZrldxm( source.getLeaderName() );
        vWjRyjbxxFjxx.setJz( source.getDriverLicense() );
        if ( source.getFirstAuxiliaryDate() != null ) {
            vWjRyjbxxFjxx.setScsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getFirstAuxiliaryDate() ) );
        }
        vWjRyjbxxFjxx.setCjmc( source.getHierarchyLevel() );
        vWjRyjbxxFjxx.setJzd( source.getResidenceAddress() );
        vWjRyjbxxFjxx.setZytc( source.getSpecialty() );
        vWjRyjbxxFjxx.setZzmm( source.getPoliticalStatus() );
        vWjRyjbxxFjxx.setSfby( source.getMilitaryService() );
        vWjRyjbxxFjxx.setRzztmc( source.getEmploymentStatus() );
        vWjRyjbxxFjxx.setGh( source.getEmployeeNumber() );
        vWjRyjbxxFjxx.setJgmc( source.getNativePlace() );
        if ( source.getStartAuxiliaryDate() != null ) {
            vWjRyjbxxFjxx.setFjgzrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getStartAuxiliaryDate() ) );
        }
        vWjRyjbxxFjxx.setZjNx( source.getOfficePhoneInner() );
        vWjRyjbxxFjxx.setBzqdmc( source.getSecurityChannel() );
        vWjRyjbxxFjxx.setHjdz( source.getRegisteredAddress() );
        vWjRyjbxxFjxx.setSjhm( source.getPhoneNumber() );
        vWjRyjbxxFjxx.setZc( source.getProfessionalTitle() );
        vWjRyjbxxFjxx.setXb( source.getGender() );
        vWjRyjbxxFjxx.setMz( source.getEthnicity() );
        vWjRyjbxxFjxx.setXxmc( source.getBloodType() );
        vWjRyjbxxFjxx.setHyzk( source.getMaritalStatus() );
        vWjRyjbxxFjxx.setXl( source.getEducationLevel() );
        vWjRyjbxxFjxx.setXm( source.getName() );
        vWjRyjbxxFjxx.setGwmc( source.getPosition() );
        vWjRyjbxxFjxx.setJkzkmc( source.getHealthStatus() );
        vWjRyjbxxFjxx.setCym( source.getFormerName() );
        if ( source.getFirstWorkDate() != null ) {
            vWjRyjbxxFjxx.setScgzsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getFirstWorkDate() ) );
        }
        if ( source.getPartyJoinDate() != null ) {
            vWjRyjbxxFjxx.setRdsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getPartyJoinDate() ) );
        }
        if ( source.getAge() != null ) {
            vWjRyjbxxFjxx.setNl( new BigDecimal( source.getAge() ) );
        }

        return vWjRyjbxxFjxx;
    }

    @Override
    public VWjRyjbxxFjxx convert(AuxiliaryPoliceInfo source, VWjRyjbxxFjxx target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getBirthDate() != null ) {
            target.setCsrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getBirthDate() ) );
        }
        else {
            target.setCsrq( null );
        }
        target.setZdbs( source.getMedicalHistory() );
        target.setLwgs( source.getLaborCompany() );
        target.setDwmc( source.getOrganization() );
        target.setZrldjh( source.getLeaderPoliceNumber() );
        target.setGmsfhm( source.getIdCard() );
        target.setZjWx( source.getOfficePhoneOuter() );
        if ( source.getAuxiliarySeniority() != null ) {
            target.setFzgl( new BigDecimal( source.getAuxiliarySeniority() ) );
        }
        else {
            target.setFzgl( null );
        }
        target.setZrldxm( source.getLeaderName() );
        target.setJz( source.getDriverLicense() );
        if ( source.getFirstAuxiliaryDate() != null ) {
            target.setScsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getFirstAuxiliaryDate() ) );
        }
        else {
            target.setScsj( null );
        }
        target.setCjmc( source.getHierarchyLevel() );
        target.setJzd( source.getResidenceAddress() );
        target.setZytc( source.getSpecialty() );
        target.setZzmm( source.getPoliticalStatus() );
        target.setSfby( source.getMilitaryService() );
        target.setRzztmc( source.getEmploymentStatus() );
        target.setGh( source.getEmployeeNumber() );
        target.setJgmc( source.getNativePlace() );
        if ( source.getStartAuxiliaryDate() != null ) {
            target.setFjgzrq( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getStartAuxiliaryDate() ) );
        }
        else {
            target.setFjgzrq( null );
        }
        target.setZjNx( source.getOfficePhoneInner() );
        target.setBzqdmc( source.getSecurityChannel() );
        target.setHjdz( source.getRegisteredAddress() );
        target.setSjhm( source.getPhoneNumber() );
        target.setZc( source.getProfessionalTitle() );
        target.setXb( source.getGender() );
        target.setMz( source.getEthnicity() );
        target.setXxmc( source.getBloodType() );
        target.setHyzk( source.getMaritalStatus() );
        target.setXl( source.getEducationLevel() );
        target.setXm( source.getName() );
        target.setGwmc( source.getPosition() );
        target.setJkzkmc( source.getHealthStatus() );
        target.setCym( source.getFormerName() );
        if ( source.getFirstWorkDate() != null ) {
            target.setScgzsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getFirstWorkDate() ) );
        }
        else {
            target.setScgzsj( null );
        }
        if ( source.getPartyJoinDate() != null ) {
            target.setRdsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getPartyJoinDate() ) );
        }
        else {
            target.setRdsj( null );
        }
        if ( source.getAge() != null ) {
            target.setNl( new BigDecimal( source.getAge() ) );
        }
        else {
            target.setNl( null );
        }

        return target;
    }
}
