package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjyxl;
import com.hl.orasync.domain.VWjRyjyxlToPoliceTrainingMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__534;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__534.class,
    uses = {ConversionUtils.class,VWjRyjyxlToPoliceTrainingMapper.class},
    imports = {}
)
public interface PoliceTrainingToVWjRyjyxlMapper extends BaseMapper<PoliceTraining, VWjRyjyxl> {
  @Mapping(
      target = "pxbmc",
      source = "trainingName"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "pxqzsj",
      source = "trainingStartDate"
  )
  @Mapping(
      target = "pxzbdwmc",
      source = "organizerName"
  )
  @Mapping(
      target = "pxzzsj",
      source = "trainingEndDate"
  )
  VWjRyjyxl convert(PoliceTraining source);

  @Mapping(
      target = "pxbmc",
      source = "trainingName"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "pxqzsj",
      source = "trainingStartDate"
  )
  @Mapping(
      target = "pxzbdwmc",
      source = "organizerName"
  )
  @Mapping(
      target = "pxzzsj",
      source = "trainingEndDate"
  )
  VWjRyjyxl convert(PoliceTraining source, @MappingTarget VWjRyjyxl target);
}
