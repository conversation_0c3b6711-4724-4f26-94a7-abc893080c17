package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnGwth;
import com.hl.orasync.domain.VWjZnGwthToPoliceChildrenForeignMarriageMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__534;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__534.class,
    uses = {ConversionUtils.class,VWjZnGwthToPoliceChildrenForeignMarriageMapper.class},
    imports = {}
)
public interface PoliceChildrenForeignMarriageToVWjZnGwthMapper extends BaseMapper<PoliceChildrenForeignMarriage, VWjZnGwth> {
  @Mapping(
      target = "xmZn",
      source = "childName"
  )
  @Mapping(
      target = "gzdwZnpo",
      source = "workStudyUnit"
  )
  @Mapping(
      target = "gjZnpo",
      source = "spouseCountry"
  )
  @Mapping(
      target = "zwZnpo",
      source = "position"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "xmZnpo",
      source = "spouseName"
  )
  @Mapping(
      target = "djrq",
      source = "registrationDate"
  )
  VWjZnGwth convert(PoliceChildrenForeignMarriage source);

  @Mapping(
      target = "xmZn",
      source = "childName"
  )
  @Mapping(
      target = "gzdwZnpo",
      source = "workStudyUnit"
  )
  @Mapping(
      target = "gjZnpo",
      source = "spouseCountry"
  )
  @Mapping(
      target = "zwZnpo",
      source = "position"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "xmZnpo",
      source = "spouseName"
  )
  @Mapping(
      target = "djrq",
      source = "registrationDate"
  )
  VWjZnGwth convert(PoliceChildrenForeignMarriage source, @MappingTarget VWjZnGwth target);
}
