package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMarriageStatus;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T08:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrHyzkToPoliceMarriageStatusMapperImpl implements VWjBrHyzkToPoliceMarriageStatusMapper {

    @Override
    public PoliceMarriageStatus convert(VWjBrHyzk source) {
        if ( source == null ) {
            return null;
        }

        PoliceMarriageStatus policeMarriageStatus = new PoliceMarriageStatus();

        policeMarriageStatus.setIdCard( source.getGmsfhm() );
        policeMarriageStatus.setChangeDate( ConversionUtils.strToDate( source.getBhsj() ) );
        policeMarriageStatus.setName( source.getXm() );
        policeMarriageStatus.setChangeStatus( source.getBhhyztmc() );
        policeMarriageStatus.setMarriageStatus( source.getHyztmc() );

        return policeMarriageStatus;
    }

    @Override
    public PoliceMarriageStatus convert(VWjBrHyzk source, PoliceMarriageStatus target) {
        if ( source == null ) {
            return target;
        }

        target.setIdCard( source.getGmsfhm() );
        target.setChangeDate( ConversionUtils.strToDate( source.getBhsj() ) );
        target.setName( source.getXm() );
        target.setChangeStatus( source.getBhhyztmc() );
        target.setMarriageStatus( source.getHyztmc() );

        return target;
    }
}
