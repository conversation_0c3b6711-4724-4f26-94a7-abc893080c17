package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyVehicles;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T08:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjQtClxxToPoliceFamilyVehiclesMapperImpl implements VWjQtClxxToPoliceFamilyVehiclesMapper {

    @Override
    public PoliceFamilyVehicles convert(VWjQtClxx source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyVehicles policeFamilyVehicles = new PoliceFamilyVehicles();

        policeFamilyVehicles.setSaleAmount( ConversionUtils.strToBigDecimal( source.getCsjg() ) );
        policeFamilyVehicles.setVehicleBrand( source.getClpp() );
        policeFamilyVehicles.setLicensePlate( source.getHphm() );
        policeFamilyVehicles.setVehicleSource( source.getCllymc() );
        policeFamilyVehicles.setOwnerName( source.getXmCqr() );
        policeFamilyVehicles.setIdCard( source.getGmsfhm() );
        policeFamilyVehicles.setTransactionAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );
        policeFamilyVehicles.setSaleDate( ConversionUtils.strToDate( source.getCssj() ) );
        policeFamilyVehicles.setTransactionDate( ConversionUtils.strToDate( source.getGmsj() ) );
        policeFamilyVehicles.setVehicleDisposition( source.getClqxmc() );

        return policeFamilyVehicles;
    }

    @Override
    public PoliceFamilyVehicles convert(VWjQtClxx source, PoliceFamilyVehicles target) {
        if ( source == null ) {
            return target;
        }

        target.setSaleAmount( ConversionUtils.strToBigDecimal( source.getCsjg() ) );
        target.setVehicleBrand( source.getClpp() );
        target.setLicensePlate( source.getHphm() );
        target.setVehicleSource( source.getCllymc() );
        target.setOwnerName( source.getXmCqr() );
        target.setIdCard( source.getGmsfhm() );
        target.setTransactionAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );
        target.setSaleDate( ConversionUtils.strToDate( source.getCssj() ) );
        target.setTransactionDate( ConversionUtils.strToDate( source.getGmsj() ) );
        target.setVehicleDisposition( source.getClqxmc() );

        return target;
    }
}
