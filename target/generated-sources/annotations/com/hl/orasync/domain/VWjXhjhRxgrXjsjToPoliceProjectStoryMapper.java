package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectStory;
import com.hl.archive.domain.entity.PoliceProjectStoryToVWjXhjhRxgrXjsjMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__534;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__534.class,
    uses = {ConversionUtils.class,PoliceProjectStoryToVWjXhjhRxgrXjsjMapper.class},
    imports = {}
)
public interface VWjXhjhRxgrXjsjToPoliceProjectStoryMapper extends BaseMapper<VWjXhjhRxgrXjsj, PoliceProjectStory> {
  @Mapping(
      target = "registeredBy",
      source = "djrxm"
  )
  @Mapping(
      target = "registerTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "title",
      source = "xjsjBt"
  )
  @Mapping(
      target = "content",
      source = "xjsjNr"
  )
  @Mapping(
      target = "xhjhZjbh",
      source = "jlXxzjbh"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectStory convert(VWjXhjhRxgrXjsj source);

  @Mapping(
      target = "registeredBy",
      source = "djrxm"
  )
  @Mapping(
      target = "registerTime",
      source = "djsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "title",
      source = "xjsjBt"
  )
  @Mapping(
      target = "content",
      source = "xjsjNr"
  )
  @Mapping(
      target = "xhjhZjbh",
      source = "jlXxzjbh"
  )
  @Mapping(
      target = "zjbh",
      source = "xxzjbh"
  )
  PoliceProjectStory convert(VWjXhjhRxgrXjsj source, @MappingTarget PoliceProjectStory target);
}
