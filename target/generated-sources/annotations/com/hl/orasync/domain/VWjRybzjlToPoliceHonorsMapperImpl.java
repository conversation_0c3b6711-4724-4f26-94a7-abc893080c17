package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-16T08:42:46+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRybzjlToPoliceHonorsMapperImpl implements VWjRybzjlToPoliceHonorsMapper {

    @Override
    public PoliceHonors convert(VWjRybzjl source) {
        if ( source == null ) {
            return null;
        }

        PoliceHonors policeHonors = new PoliceHonors();

        policeHonors.setApproveAuthority( source.getJcpjjgmc() );
        policeHonors.setIdCard( source.getGmsfhm() );
        policeHonors.setBh( source.getBh() );
        policeHonors.setHonorName( source.getJcmc() );
        policeHonors.setAwardDate( ConversionUtils.strToLocalDate( source.getJcsj() ) );

        return policeHonors;
    }

    @Override
    public PoliceHonors convert(VWjRybzjl source, PoliceHonors target) {
        if ( source == null ) {
            return target;
        }

        target.setApproveAuthority( source.getJcpjjgmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setBh( source.getBh() );
        target.setHonorName( source.getJcmc() );
        target.setAwardDate( ConversionUtils.strToLocalDate( source.getJcsj() ) );

        return target;
    }
}
