package com.hl.archive.domain.entity;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 人员相关预警记录表
 */
@ApiModel(description = "人员相关预警记录表")
@Data
@TableName(value = "police_person_warn_record")
public class PolicePersonWarnRecord {
    /**
     * 信息主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "信息主键ID")
    @JsonSerialize(using =  ToStringSerializer.class)
    @ExcelIgnore
    private Long id;

    /**
     * 身份证
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证")
    @ExcelProperty(value = "身份证号" )
    private String idCard;

    /**
     * 人员姓名
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "人员姓名")
    @ExcelProperty(value = "姓名" )
    private String name;

    /**
     * 警号
     */
    @TableField(value = "police_number")
    @ApiModelProperty(value = "警号")
    @ExcelProperty(value = "警号" )
    private String policeNumber;

    /**
     * 所属单位
     */
    @TableField(value = "organization_id")
    @ApiModelProperty(value = "所属单位")
    @ExcelProperty(value = "所属单位" )
    private String organizationId;

    /**
     * 时间
     */
    @TableField(value = "warn_time")
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "预警时间" )
    private LocalDateTime warnTime;

    /**
     * 预警类型
     */
    @TableField(value = "warn_type")
    @ApiModelProperty(value = "预警类型")
    @ExcelProperty(value = "预警类型" )
    private String warnType;

    /**
     * 预警数据主键
     */
    @TableField(value = "data_key")
    @ApiModelProperty(value = "预警数据主键")
    @ExcelIgnore
    private String dataKey;


    @TableField(value = "data_type")
    @ApiModelProperty(value = "数据类型")
    @ExcelProperty(value = "预警类型" )
    private String dataType;



    /**
     * 情况说明
     */
    @TableField(value = "description")
    @ApiModelProperty(value = "情况说明")
    @ExcelProperty(value = "简要情况" )
    private String description;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    @ExcelIgnore
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value = "创建时间")
    @ExcelIgnore
    private LocalDateTime createAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    @ExcelIgnore
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value = "修改时间")
    @ExcelIgnore
    private LocalDateTime updateAt;

    /**
     * 是否删除（0-否，1-是）
     */
    @TableField(value = "is_deleted")
    @TableLogic
    @ApiModelProperty(value = "是否删除（0-否，1-是）")
    @ExcelIgnore
    private Boolean isDeleted;

    @TableField(exist = false)
    @Translation(type = TransConstants.ORGANIZATION_TO_NAME, mapper = "organizationId")
    @ExcelIgnore
    private String organizationName;


    @TableField(value = "sign_status")
    @ExcelIgnore
    private Integer signStatus;

    @TableField(value = "feedback_type")
    @ExcelIgnore
    private String feedbackType;

    @TableField(value = "feedback_content")
    @ExcelIgnore
    private String feedbackContent;
}