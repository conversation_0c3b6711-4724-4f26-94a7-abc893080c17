package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PoliceHobbyQueryDTO {

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("爱好名称")
    private String hobbyName;

    @ApiModelProperty("爱好类型")
    private String hobbyType;

    @ApiModelProperty("组织机构ID")
    private String organizationId;

    @ApiModelProperty("查询关键词")
    private String query;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("每页大小")
    private Integer limit = 10;
}
