package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PoliceHobbyBatchAddDTO {

    @ApiModelProperty("身份证号列表")
    private List<String> idCardList;

    @ApiModelProperty("爱好列表")
    private List<HobbyInfo> hobbyList;

    @Data
    public static class HobbyInfo {
        @ApiModelProperty("爱好名称")
        private String hobbyName;

        @ApiModelProperty("爱好类型")
        private String hobbyType;

        @ApiModelProperty("字典值")
        private String hobbyCode;

        @ApiModelProperty("描述")
        private String description;
    }
}
