package com.hl.archive.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.*;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PoliceHonors;
import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.archive.domain.entity.PoliceTagInfo;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.domain.request.TagDrillDownRequest;
import com.hl.archive.domain.request.TagStatisticsRequest;
import com.hl.archive.enums.TagTypeEnum;
import com.hl.archive.service.PoliceHonorsService;
import com.hl.archive.service.PoliceTagInfoService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/policeTagInfo")
@RequiredArgsConstructor
@Api(tags = "标签管理")
public class PoliceTagInfoController {

    private final PoliceTagInfoService policeTagInfoService;

    private final PoliceHonorsService policeHonorsService;



    @PostMapping("/addHonors")
    @ApiOperation("添加表彰奖励")
    public R<?> addHonor(@RequestBody PoliceHonorAddDTO requestDTO) {
        boolean result = policeHonorsService.addHonor(requestDTO);
        return R.ok(result);
    }

    @PostMapping("/pageHonors")
    @ApiOperation("分页查询表彰奖励")
    public R<List<PoliceHonors>> pageHonors(@RequestBody PoliceHonorsQueryDTO requestDTO) {
        LambdaQueryWrapper<PoliceHonors> queryWrapper = Wrappers.<PoliceHonors>lambdaQuery()
                .like(StrUtil.isNotBlank(requestDTO.getHonorName()), PoliceHonors::getHonorName, requestDTO.getHonorName())
                .eq(StrUtil.isNotBlank(requestDTO.getIdCard()), PoliceHonors::getIdCard, requestDTO.getIdCard())
                .eq(requestDTO.getSourceType() != null, PoliceHonors::getSourceType, requestDTO.getSourceType());
        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())){
            if (!"320412000000".equals(requestDTO.getOrganizationId())) {
                queryWrapper.likeRight(PoliceHonors::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }
        }
        queryWrapper.orderByDesc(PoliceHonors::getAwardDate);
        Page<PoliceHonors> list = policeHonorsService.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);

        return R.ok(list.getRecords(), (int) list.getTotal());

    }

    @PostMapping("/deleteHonors")
    @ApiOperation("删除表彰奖励")
    public R<?> deleteHonors(@RequestBody JSONObject param) {
        List<String> ids = param.getJSONArray("ids").toJavaList(String.class);
        return R.ok(policeHonorsService.removeBatchByIds(ids));
    }


    @PostMapping("/selectTagCount")
    @ApiOperation("获取档案数量")
    public R<?> selectTagCount(@RequestBody PoliceBaseQueryRequest request) {
        List<TagCountResultDTO> list = policeTagInfoService.selectTagCount(request);
        return R.ok(list);
    }

    @PostMapping("/statisticsTrainingAndCombat")
    @ApiOperation("登峰训练营和实战能力体系人员数量统计")
    public R<CombinedTagStatisticsDTO> statisticsTrainingAndCombat(@RequestBody TagStatisticsRequest request) {
        CombinedTagStatisticsDTO statistics = policeTagInfoService.getCombinedTagStatistics(request);
        return R.ok(statistics);
    }

    @PostMapping("/tagDrillDown")
    @ApiOperation("标签统计数字穿透查询 - 根据标签类型查询人员基本信息列表")
    public R<List<PoliceBasicInfo>> tagDrillDown(@RequestBody TagDrillDownRequest request) {
        Page<PoliceBasicInfo> page = policeTagInfoService.getPoliceListByTagType(request);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }





    @PostMapping("/queryPersonTag")
    @ApiOperation("查询人员标签")
    public R<List<PolicePersonalTagReturnDTO>> queryPersonTag(@RequestBody PolicePersonalTagQueryDTO param) {
        Page<PolicePersonalTagReturnDTO> list = policeTagInfoService.pagePolicePersonalTag(param);
        return R.ok(list.getRecords(), (int) list.getTotal());
    }




}
