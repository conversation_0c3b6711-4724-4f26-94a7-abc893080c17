package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceCapabilityEvalRequestDTO;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.security.utils.SsoUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.PoliceCapabilityEvalMapper;
import com.hl.archive.domain.entity.PoliceCapabilityEval;

@Service
public class PoliceCapabilityEvalService extends ServiceImpl<PoliceCapabilityEvalMapper, PoliceCapabilityEval> {

    public Page<PoliceCapabilityEval> pageList(PoliceCapabilityEvalRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceCapabilityEval> queryWrapper = Wrappers.<PoliceCapabilityEval>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getPoliceNumber()), PoliceCapabilityEval::getPoliceNumber, requestDTO.getPoliceNumber());

        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())) {
            if ("32041200000".equals(requestDTO.getOrganizationId())) {
                queryWrapper.eq(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId());
            } else {
                queryWrapper.like(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }
        }
        if (StrUtil.isNotBlank(requestDTO.getQuery())) {
            queryWrapper.and(w -> w.like(PoliceCapabilityEval::getParticipantName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPoliceNumber, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPlanName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getFeatureName, requestDTO.getQuery()));
        }
        Page<PoliceCapabilityEval> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        return page;
    }


    //    @EventListener(ApplicationReadyEvent.class)
    public void cleanOrganizationId() {
        this.list().forEach(item -> {
            item.setOrganizationId(SsoCacheUtil.getUserOrgIdByPoliceId(item.getPoliceNumber()));
            this.updateById(item);
        });
    }
}
