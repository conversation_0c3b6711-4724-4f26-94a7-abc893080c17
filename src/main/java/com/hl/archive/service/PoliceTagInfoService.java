package com.hl.archive.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.*;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.archive.domain.entity.PoliceTagInfo;
import com.hl.archive.domain.entity.PoliceTagInfoXjdf;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.domain.request.TagDrillDownRequest;
import com.hl.archive.domain.request.TagStatisticsRequest;
import com.hl.archive.enums.TagTypeEnum;
import com.hl.archive.mapper.PoliceTagInfoMapper;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 民警标签信息服务
 * 处理四种标签类型：登封训练营、实战能力体系、重点关注、表彰奖励
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PoliceTagInfoService extends ServiceImpl<PoliceTagInfoMapper, PoliceTagInfo> {

    private final PoliceTagInfoMapper policeTagInfoMapper;

    private final PoliceProjectEntryPersonService policeProjectEntryPersonService;

    private final PoliceTagInfoXjdfService policeTagInfoXjdfService;

    /**
     * 获取标签统计信息
     */
    public TagStatisticsDTO getTagStatistics(TagStatisticsRequest request) {
        // 验证标签类型
        TagTypeEnum tagTypeEnum = TagTypeEnum.getByCode(request.getTagType());
        if (tagTypeEnum == null) {
            throw new IllegalArgumentException("无效的标签类型: " + request.getTagType());
        }

//        // 获取标签信息列表
//        List<PoliceTagInfo> tagInfoList = policeTagInfoMapper.getTagInfoByType(request);
        LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
                .eq(PoliceTagInfoXjdf::getTagType, request.getTagType());
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            if ("320412000000".equals(request.getOrganizationId())) {
                queryWrapper.eq(PoliceTagInfoXjdf::getOrganizationId, "320412000000");
            }else {
                queryWrapper.like(PoliceTagInfoXjdf::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }


        }
        List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(queryWrapper);

        // 创建统计结果
        TagStatisticsDTO statistics = new TagStatisticsDTO();
        statistics.setTagType(request.getTagType());
        statistics.setTagTypeName(tagTypeEnum.getName());

        // 统计总人数和详细标签
        Set<String> uniquePersons = new HashSet<>();
        Map<String, Set<String>> tagPersonMap = new HashMap<>();

        for (PoliceTagInfoXjdf policeTagInfoXjdf : list) {
            uniquePersons.add(policeTagInfoXjdf.getIdCard());
            tagPersonMap.computeIfAbsent(policeTagInfoXjdf.getTagName(), k -> new HashSet<>()).add(policeTagInfoXjdf.getIdCard());
        }

        // 设置总人数
        statistics.setTotalCount(uniquePersons.size());

        // 设置详细统计
        List<TagStatisticsDTO.TagDetailStatistics> tagDetails = new ArrayList<>();
        for (Map.Entry<String, Set<String>> entry : tagPersonMap.entrySet()) {
            TagStatisticsDTO.TagDetailStatistics detail = new TagStatisticsDTO.TagDetailStatistics();
            detail.setTagName(entry.getKey());
            detail.setCount(entry.getValue().size());
            tagDetails.add(detail);
        }

        // 按人数降序排序
        tagDetails.sort((a, b) -> b.getCount().compareTo(a.getCount()));
        statistics.setTagDetails(tagDetails);

        return statistics;
    }

    /**
     * 获取登峰训练营和实战能力体系组合统计信息
     */
    public CombinedTagStatisticsDTO getCombinedTagStatistics(TagStatisticsRequest request) {
        CombinedTagStatisticsDTO combinedStatistics = new CombinedTagStatisticsDTO();

        // 获取登峰训练营统计
        TagStatisticsRequest dengfengRequest = new TagStatisticsRequest();
        dengfengRequest.setOrganizationId(request.getOrganizationId());
        dengfengRequest.setTagType(TagTypeEnum.DENGFENG_TRAINING.getCode());
        TagStatisticsDTO dengfengStatistics = getTagStatistics(dengfengRequest);
        dengfengStatistics.setTagDetails(null);
        combinedStatistics.setDengfengTraining(dengfengStatistics);

        // 获取实战能力体系统计
        TagStatisticsRequest combatRequest = new TagStatisticsRequest();
        combatRequest.setOrganizationId(request.getOrganizationId());
        combatRequest.setTagType(TagTypeEnum.COMBAT_ABILITY.getCode());
        TagStatisticsDTO combatStatistics = getTagStatistics(combatRequest);
        combinedStatistics.setCombatAbility(combatStatistics);


        // 获取星火计划统计
        TagStatisticsRequest xinghuoRequest = new TagStatisticsRequest();
        xinghuoRequest.setOrganizationId(request.getOrganizationId());
        xinghuoRequest.setTagType(TagTypeEnum.XINGHUO_PLAN.getCode());
        TagStatisticsDTO xinghuoStatistics = getXinghuoStatistics(xinghuoRequest);
        combinedStatistics.setXinghuoPlan(xinghuoStatistics);

        // 添加警营先锋
        TagStatisticsRequest jingyingRequest = new TagStatisticsRequest();
        jingyingRequest.setOrganizationId(request.getOrganizationId());
        jingyingRequest.setTagType(TagTypeEnum.JINGYING_XIANFENG.getCode());
        TagStatisticsDTO jingyingStatistics = getTagStatistics(jingyingRequest);
        jingyingStatistics.setTagDetails(null);
        combinedStatistics.setJingyingXianfeng(jingyingStatistics);

        return combinedStatistics;
    }


    public TagStatisticsDTO getXinghuoStatistics(TagStatisticsRequest request) {
        LambdaQueryWrapper<PoliceProjectEntryPerson> queryWrapper = Wrappers.<PoliceProjectEntryPerson>lambdaQuery();
        if (StrUtil.isNotBlank(request.getOrganizationId())) {
            queryWrapper.likeRight(StrUtil.isNotBlank(request.getOrganizationId()) && !"320412000000".equals(request.getOrganizationId()),
                    PoliceProjectEntryPerson::getOrganizationId, request.getOrganizationId().substring(0, 8));
        }

        long count = policeProjectEntryPersonService.count(queryWrapper);
        TagStatisticsDTO statistics = new TagStatisticsDTO();
        statistics.setTagType(TagTypeEnum.XINGHUO_PLAN.getCode());
        statistics.setTagTypeName(TagTypeEnum.XINGHUO_PLAN.getName());
        statistics.setTotalCount((int) count);
        return statistics;
    }

    /**
     * 标签穿透查询
     */
    public Page<PoliceBasicInfo> getPoliceListByTagType(TagDrillDownRequest request) {
        // 验证标签类型
        TagTypeEnum tagTypeEnum = TagTypeEnum.getByCode(request.getTagType());
        if (tagTypeEnum == null) {
            throw new IllegalArgumentException("无效的标签类型: " + request.getTagType());
        }
        // 收集所有相关的身份证号
        Set<String> idCardSet;
        if (request.getTagType().equals(TagTypeEnum.XINGHUO_PLAN.getCode())) {
            LambdaQueryWrapper<PoliceProjectEntryPerson> queryWrapper = Wrappers.<PoliceProjectEntryPerson>lambdaQuery();
            if (StrUtil.isNotBlank(request.getOrganizationId())) {
                queryWrapper.likeRight(StrUtil.isNotBlank(request.getOrganizationId()) && !"320412000000".equals(request.getOrganizationId()),
                        PoliceProjectEntryPerson::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
            List<PoliceProjectEntryPerson> list = policeProjectEntryPersonService.list(queryWrapper);

            idCardSet = list.stream().map(PoliceProjectEntryPerson::getIdCard).collect(Collectors.toSet());

        } else {
            // 获取标签信息列表
            LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
                    .eq(PoliceTagInfoXjdf::getTagType, request.getTagType())
                    .like(StrUtil.isNotBlank(request.getTagName()), PoliceTagInfoXjdf::getTagName, request.getTagName());
            if (StrUtil.isNotBlank(request.getOrganizationId())){
                queryWrapper.like(StrUtil.isNotBlank(request.getOrganizationId()) && !"320412000000".equals(request.getOrganizationId()),
                        PoliceTagInfoXjdf::getOrganizationId, request.getOrganizationId().substring(0, 8));
            }
            List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(queryWrapper);
            idCardSet = list.stream().map(PoliceTagInfoXjdf::getIdCard).collect(Collectors.toSet());
        }


        // 如果没有找到相关人员，返回空结果
        if (idCardSet.isEmpty()) {
            return new Page<>(request.getPage(), request.getLimit());
        }

        // 转换为List
        List<String> idCardList = new ArrayList<>(idCardSet);

        // 如果有组织ID，截取前8位
        String organizationId = request.getOrganizationId();
        if (StrUtil.isNotBlank(organizationId)) {
            if ("320412000000".equals(organizationId)) {
                organizationId = null;
            } else {
                organizationId = organizationId.substring(0, 8);
            }
        }


        // 创建分页对象并执行查询
        Page<PoliceBasicInfo> page = new Page<>(request.getPage(), request.getLimit());
        return policeTagInfoMapper.getPoliceByIdCards(page, idCardList, organizationId);
    }


    public List<TagCountResultDTO> selectTagCount(PoliceBaseQueryRequest request) {

        List<PoliceTagInfoXjdf> list = policeTagInfoXjdfService.list(Wrappers.<PoliceTagInfoXjdf>lambdaQuery()
                .eq(PoliceTagInfoXjdf::getIdCard, request.getIdCard())
                .in(PoliceTagInfoXjdf::getTagType, ListUtil.of(TagTypeEnum.DENGFENG_TRAINING.getCode(), TagTypeEnum.COMBAT_ABILITY.getCode())));


        Map<String, List<PoliceTagInfoXjdf>> tagType = list.stream()
                .collect(Collectors.groupingBy(PoliceTagInfoXjdf::getTagType));

        List<TagCountResultDTO> result = new ArrayList<>();

        for (Map.Entry<String, List<PoliceTagInfoXjdf>> entry : tagType.entrySet()) {
            String key = entry.getKey();
            List<PoliceTagInfoXjdf> value = entry.getValue();
            List<TagCountResultDTO.TagDetail> tagNameList = new ArrayList<>();
            for (PoliceTagInfoXjdf tagInfo : value) {
                String tagName = tagInfo.getTagName();
                LocalDate awardDate = tagInfo.getAwardDate();

                TagCountResultDTO.TagDetail tagDetail = new TagCountResultDTO.TagDetail();
                tagDetail.setTagName(tagName);
                tagDetail.setAwardDate(awardDate);
                tagNameList.add(tagDetail);

            }
            TagCountResultDTO resultDTO = new TagCountResultDTO();
            resultDTO.setTagType(key);
            resultDTO.setTagNameList(tagNameList);
            resultDTO.setTagTypeName(TagTypeEnum.getByCode(key).getName());
            result.add(resultDTO);
        }
        return result;
    }

    public Page<PolicePersonalTagReturnDTO> pagePolicePersonalTag(PolicePersonalTagQueryDTO param) {
        if (StrUtil.isNotBlank(param.getOrganizationId())) {
            if (!"320412000000".equals(param.getOrganizationId())) {
                param.setOrganizationId(param.getOrganizationId().substring(0, 8));
            } else {
                param.setOrganizationId("320412000000");
            }
        } else {
            param.setOrganizationId(null);
        }

        Page<PolicePersonalTagReturnDTO> page = policeTagInfoMapper.pagePolicePersonalTag(Page.of(param.getPage(), param.getLimit()), param);
        return page;
    }
}

